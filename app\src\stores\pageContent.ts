import { ref, computed } from "vue";
import { defineStore } from "pinia";

interface IPageContent {
	error?: string;
	data?: {
		id: number;
		documentId: string;
		slug: string;
		title: string;
		locale: string;
		content: {
			__component: string;
			id: number;
			Header: string;
			Content: string;
		}[];
	};
}

import type { App } from "./../../../server/src/index";
import { treaty } from "@elysiajs/eden";

const client = treaty<App>("localhost:3000");

export const usePageContentStore = defineStore("pageContent", () => {
	const pageContent = ref<IPageContent>({ error: undefined, data: undefined });

	const fetchPageContent = async (slug: string) => {
		const r = await client.strapi["web-content"].get({ query: { slug: slug } });

    if (r.data.error && r.data.error !== null) {
			pageContent.value = {
				error: r.data.error,
				data: undefined,
			};
			return;
		}

		pageContent.value.data = r.data;
	};

	return {
		pageContent,
		fetchPageContent,
	};
});
