<script setup lang="ts">
</script>

<template>
<div class="min-h-screen bg-neutral-800 flex justify-center items-center flex-col gap-4">
  <h1 class="text-neutral-100 text-3xl font-bold">Another View</h1>
  <div class="flex flex-col items-center gap-2">
    <div class="flex gap-2">
      <button @click="" class="px-4 py-2 bg-red-500/30 text-neutral-100 rounded-sm">Clear the counter</button>
      <button @click="" class="px-4 py-2 bg-neutral-100/30 text-neutral-100 rounded-sm">Click me!</button>
    </div>
  </div>
  <RouterLink to="/" class="text-neutral-100  px-4 py-2 border border-neutral-100/30 rounded-sm">Go to Home</RouterLink>
</div>
</template>

<style scoped>

</style>
